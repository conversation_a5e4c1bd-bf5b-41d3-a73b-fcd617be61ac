<template>
  <basic-card>
    <template slot="right">
      <div>
        <el-button type="primary" @click="exportHandle"> 导出 </el-button>
        <el-button
          type="primary"
          v-permission="routeButtonsPermission.INFO_MAINTAIN"
          @click="infoMaintainHandle"
          >{{ routeButtonsTitle.INFO_MAINTAIN }}</el-button
        >
        <el-button
          type="primary"
          v-permission="routeButtonsPermission.MAKE_INVOICE"
          @click="invoiceCreateHandle"
          >{{ routeButtonsTitle.MAKE_INVOICE }}</el-button
        >
      </div>
    </template>
    <InvoiceInfo />
    <basic-tab
      :tabs-data="parkOptions"
      :current="extralQuerys.parkIds"
      :disabled="reqLoading"
      @tabsChange="parkChange"
    >
      <template v-slot:right>
        <el-switch
          @change="updateData"
          :disabled="reqLoading"
          v-model="extralQuerys.selfFlag"
          active-text="只看我录入的"
        >
        </el-switch>
      </template>
    </basic-tab>

    <div class="flex justify-content-between m-t-16">
      <BasicSwitchTab
        width="96"
        :tabs="tabsData"
        :current="extralQuerys.examineStatus"
        :disabled="reqLoading"
        @change="tabsChange"
      />

      <div class="w100 flex flex-wrap justify-content-end">
        <el-input
          class="m-l-8 m-b-8"
          style="width: 200px"
          v-model="extralQuerys.buyerName"
          placeholder="请输入客户名称"
          clearable
          @change="searchTableHandle"
        />
        <el-date-picker
          class="m-l-8 m-b-8"
          v-model="applyTime"
          type="daterange"
          align="right"
          unlink-panels
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd"
          :picker-options="pickerOptions"
          @change="dateTimeChange"
        />
        <el-select
          class="m-l-8 m-b-8"
          v-model="extralQuerys.openTypeInvoicing"
          :popper-append-to-body="true"
          collapse-tags
          clearable
          placeholder="开票类型"
          @change="searchTableHandle"
        >
          <el-option
            v-for="item in invType"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
        <el-select
          class="m-l-8"
          v-model="extralQuerys.uploadInv"
          :popper-append-to-body="true"
          collapse-tags
          clearable
          placeholder="发票上传"
          @change="searchTableHandle"
        >
          <el-option
            v-for="item in invoiceOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </div>
    </div>

    <drive-table
      class="m-t-16"
      ref="drive-table"
      :columns="tableColumn"
      :searchQuerysHook="searchQuerysHook"
      :extral-querys="extralQuerys"
      :api-fn="invApplyRecordPage"
      @setLoading="setLoading"
    />

    <!-- 发票上传-->
    <invoice-upload
      @updateData="updateData"
      :id="invoiceUploadId"
      ref="invoiceUpload"
      :visible.sync="visible"
    />

  </basic-card>
</template>

<script>
import ColumnMixin from './column'
import {
  getApplyExport,
  invApplyRecordExamineStatus,
  invApplyRecordInvType,
  invApplyRecordPage,
  getPark,
  cancel_destroy,
  withdraw
} from '../api'
import downloads from '@/utils/download'
import InvoiceInfo from '../components/InvoiceInfo'
import BasicTab from '@/components/BasicTab'
import { formatGetParams } from '@/utils/tools'
import BasicSwitchTab from '@/components/BasicSwitchTab'
import InvoiceUpload from '../InvoiceUpload'
import dayjs from 'dayjs'

export default {
  name: 'DrawBillList',
  components: { BasicSwitchTab, BasicTab, InvoiceUpload,InvoiceInfo},
  mixins: [ColumnMixin],
  data() {
    return {
      invoiceUploadId: undefined,
      visible: false,
      current: 0,
      invApplyRecordPage,
      tabsData: [],
      applyTime: [],
      extralQuerys: {
        examineStatus: '',
        buyerName: '',
        openTypeInvoicing: '',
        parkIds: -1,
        selfFlag: false
      },
      reqLoading: false,
      invoiceOptions: [
        {
          label: '已上传',
          value: true
        },
        {
          label: '未上传',
          value: false
        }
      ],
      pickerOptions: {
        shortcuts: [
          {
            text: '今日',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime())
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '本周',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(
                start.getTime() - 3600 * 1000 * 24 * (start.getDay() - 1)
              )
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '本月',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setDate(1)
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '本季度',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              const month = start.getMonth()
              if (month < 3) {
                start.setMonth(0)
              } else if (month < 6) {
                start.setMonth(3)
              } else if (month < 9) {
                start.setMonth(6)
              } else {
                start.setMonth(9)
              }
              start.setDate(1)
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '本年',

            onClick(picker) {
              const end = new Date()
              const start = new Date()
              //起始时间为本年的第一天
              start.setDate(1)
              start.setMonth(0)
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '近3年',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 365 * 3)
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '3年以前',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 365 * 10)
              end.setTime(end.getTime() - 3600 * 1000 * 24 * 365 * 3)
              picker.$emit('pick', [start, end])
            }
          }
        ]
      },
      invType: [],
      parkOptions: [],

    }
  },
  activated() {
    this.getExamineStatus()
    this.getInvType()
    this.getPark()
    if (this.executeActivated) {
      this.$refs['drive-table'] && this.$refs['drive-table'].refreshTable()
    }
  },
  methods: {
    withdraw({ id, destroyFlag,destroyOrderId }) {
      this.$confirm('是否确认撤回申请?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const api = destroyFlag ? cancel_destroy : withdraw
        const params = destroyFlag
          ? { orderId:destroyOrderId }
          : { id };
        api(params).then(() => {
          this.$toast.success('撤回成功')
          this.$refs['drive-table'].resetPageNoRefreshTable()
        })
      })
    },
    setLoading(val) {
      this.reqLoading = val
    },
    invoiceUpload(row) {
      this.visible = true
      this.invoiceUploadId = row.id
    },
    getPark() {
      getPark().then(res => {
        this.parkOptions = [
          { label: '全部', value: -1 },
          ...res.map(item => ({
            label: item.label,
            value: item.key
          }))
        ]
      })
    },
    goJournalDetailHandle(row) {
      this.$router.push({
        path: '/journal/journalDetail',
        query: {
          id: row.claimId
        }
      })
    },
    exportHandle() {
      const params = {
        ...this.$refs['drive-table'].querys,
        ...this.extralQuerys
      }
      let url = getApplyExport() + '?'
      url += formatGetParams(params)
      downloads.requestDownload(
        url,
        'excel',
        dayjs().format('YYYY-MM-DD') + '开票记录.xls'
      )
    },
    dateTimeChange(e) {
      const [beginApplyTime = '', endApplyTime = ''] = e || []
      this.extralQuerys.beginApplyTime = beginApplyTime
      this.extralQuerys.endApplyTime = endApplyTime
      this.searchTableHandle()
    },
    searchTableHandle() {
      this.$refs['drive-table'].resetPageNoRefreshTable()
    },
    getInvType() {
      invApplyRecordInvType().then(res => {
        this.invType = res.map(item => {
          return {
            label: item.label,
            value: item.key
          }
        })
      })
    },
    getExamineStatus() {
      invApplyRecordExamineStatus().then(res => {
        const list = res.map(item => {
          return {
            label: item.label,
            value: item.key
          }
        })
        this.tabsData = [{ label: '全部', value: '' }, ...list]
      })
    },
    editHandle(row) {
      this.$router.push({
        path: '/drawBill/drawBillList/invoiceCreate',
        query: {
          id: row.id
        }
      })
    },
    detailHandle(row) {
      this.$router.push({
        path: '/drawBill/drawBillList/drawBillDetails',
        query: {
          id: row.id,
          orderId: row.orderId,
          invoiceId: row.destroyOrderId
        }
      })
    },
    invoiceCreateHandle() {
      this.$router.push('/drawBill/drawBillList/invoiceCreate')
    },
    infoMaintainHandle() {
      this.$router.push('/drawBill/drawBillList/infoMaintain')
    },
    tabsChange(e) {
      this.reqLoading = true
      this.extralQuerys.examineStatus = e
      this.$refs['drive-table']?.resetPageNoRefreshTable()
    },
    parkChange(e) {
      this.extralQuerys.parkIds = e
      this.$refs['drive-table']?.resetPageNoRefreshTable()
    },
    updateData() {
      this.$refs['drive-table']?.resetPageNoRefreshTable()
    },
    searchQuerysHook(e) {
      const { applyTime } = e
      if (applyTime && applyTime.length > 0) {
        const [beginApplyTime, endApplyTime] = applyTime
        delete e.applyTime
        return {
          ...e,
          beginApplyTime,
          endApplyTime
        }
      } else {
        return e
      }
    }
  }
}
</script>

<style scoped lang="scss">
.underline {
  text-decoration: underline;
}

:deep(.operate-left) {
  width: 100%;
}
</style>

<style lang="scss">
.underline {
  text-decoration: line-through;
}
</style>
