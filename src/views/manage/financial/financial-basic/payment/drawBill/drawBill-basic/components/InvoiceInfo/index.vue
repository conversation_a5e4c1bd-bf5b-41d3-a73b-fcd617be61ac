<template>
  <div class="invoice-info-container">
    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 左侧统计区域 -->
      <div class="left-statistics">
        <div class="statistics-header">
          <span class="header-text">自主开票的黄目</span>
          <span class="statistics-label">统计数据</span>
        </div>
        <div class="statistics-list">
          <div
            v-for="item in statisticsData"
            :key="item.key"
            class="statistics-item"
            :class="{ 'highlight': item.highlight }"
          >
            <span class="item-label">{{ item.label }}</span>
            <span class="item-percentage">{{ item.percentage }}%</span>
          </div>
        </div>
      </div>

      <!-- 中间普通票发票字段 -->
      <div class="middle-form">
        <div class="form-header">
          <span class="header-text">普通票发票字段</span>
        </div>
        <div class="form-content">
          <div class="form-row">
            <div class="form-item">
              <label class="form-label">开票主体</label>
              <el-select
                v-model="formData.invoiceBody"
                placeholder="请选择开票主体"
                clearable
                class="form-input"
              >
                <el-option
                  v-for="item in invoiceBodyOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </div>
          </div>

          <div class="form-row">
            <div class="form-item">
              <label class="form-label">单位</label>
              <el-input
                v-model="formData.unit"
                placeholder="请输入单位"
                clearable
                class="form-input"
              />
            </div>
          </div>

          <div class="form-row">
            <div class="form-item">
              <label class="form-label">税号</label>
              <el-input
                v-model="formData.taxNumber"
                placeholder="请输入税号"
                clearable
                class="form-input"
              />
            </div>
          </div>

          <div class="form-row">
            <div class="form-item">
              <label class="form-label">地址</label>
              <el-input
                v-model="formData.address"
                placeholder="请输入地址"
                clearable
                class="form-input"
              />
            </div>
          </div>

          <div class="form-row">
            <div class="form-item">
              <label class="form-label">电话</label>
              <el-input
                v-model="formData.phone"
                placeholder="请输入电话"
                clearable
                class="form-input"
              />
            </div>
          </div>

          <div class="form-row">
            <div class="form-item">
              <label class="form-label">开户行</label>
              <el-input
                v-model="formData.bankName"
                placeholder="请输入开户行"
                clearable
                class="form-input"
              />
            </div>
          </div>

          <div class="form-row">
            <div class="form-item">
              <label class="form-label">账号</label>
              <el-input
                v-model="formData.bankAccount"
                placeholder="请输入账号"
                clearable
                class="form-input"
              />
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧特殊票种字段 -->
      <div class="right-form">
        <div class="form-header">
          <span class="header-text">特殊票种发票字段-不动产租赁</span>
        </div>
        <div class="form-content">
          <div class="form-row">
            <div class="form-item">
              <label class="form-label">*不动产地址</label>
              <el-input
                v-model="formData.realEstateAddress"
                placeholder="请输入不动产地址"
                clearable
                class="form-input"
              />
            </div>
          </div>

          <div class="form-row">
            <div class="form-item">
              <label class="form-label">详细地址</label>
              <el-input
                v-model="formData.detailAddress"
                placeholder="请输入详细地址"
                clearable
                class="form-input"
              />
            </div>
          </div>

          <div class="form-row">
            <div class="form-item">
              <label class="form-label">起始期间</label>
              <el-date-picker
                v-model="formData.startPeriod"
                type="date"
                placeholder="选择起始期间"
                value-format="yyyy-MM-dd"
                class="form-input"
              />
            </div>
          </div>

          <div class="form-row">
            <div class="form-item">
              <label class="form-label">终止期间</label>
              <el-date-picker
                v-model="formData.endPeriod"
                type="date"
                placeholder="选择终止期间"
                value-format="yyyy-MM-dd"
                class="form-input"
              />
            </div>
          </div>

          <div class="form-row">
            <div class="form-item">
              <label class="form-label">产权证书/不动产权证号</label>
              <el-input
                v-model="formData.propertyCertificate"
                placeholder="请输入证书号"
                clearable
                class="form-input"
              />
            </div>
          </div>

          <div class="form-row">
            <div class="form-item">
              <label class="form-label">房屋性质</label>
              <el-select
                v-model="formData.houseNature"
                placeholder="请选择房屋性质"
                clearable
                class="form-input"
              >
                <el-option label="住宅" value="residential" />
                <el-option label="商业" value="commercial" />
                <el-option label="办公" value="office" />
              </el-select>
            </div>
          </div>
        </div>

        <!-- 特殊票种发票字段-不动产销售 -->
        <div class="form-header m-t-16">
          <span class="header-text">特殊票种发票字段-不动产销售</span>
        </div>
        <div class="form-content">
          <div class="form-row">
            <div class="form-item">
              <label class="form-label">*不动产地址</label>
              <el-input
                v-model="formData.saleRealEstateAddress"
                placeholder="请输入不动产地址"
                clearable
                class="form-input"
              />
            </div>
          </div>

          <div class="form-row">
            <div class="form-item">
              <label class="form-label">详细地址</label>
              <el-input
                v-model="formData.saleDetailAddress"
                placeholder="请输入详细地址"
                clearable
                class="form-input"
              />
            </div>
          </div>

          <div class="form-row">
            <div class="form-item">
              <label class="form-label">产权证书/不动产权证号</label>
              <el-input
                v-model="formData.salePropertyCertificate"
                placeholder="请输入证书号"
                clearable
                class="form-input"
              />
            </div>
          </div>

          <div class="form-row">
            <div class="form-item">
              <label class="form-label">房屋性质</label>
              <el-select
                v-model="formData.saleHouseNature"
                placeholder="请选择房屋性质"
                clearable
                class="form-input"
              >
                <el-option label="住宅" value="residential" />
                <el-option label="商业" value="commercial" />
                <el-option label="办公" value="office" />
              </el-select>
            </div>
          </div>

          <div class="form-row">
            <div class="form-item">
              <label class="form-label">黄金合同备案号</label>
              <el-input
                v-model="formData.contractRecordNumber"
                placeholder="请输入合同备案号"
                clearable
                class="form-input"
              />
            </div>
          </div>

          <div class="form-row">
            <div class="form-item">
              <label class="form-label">土地增值税项目编号</label>
              <el-input
                v-model="formData.landValueTaxNumber"
                placeholder="请输入项目编号"
                clearable
                class="form-input"
              />
            </div>
          </div>

          <div class="form-row">
            <div class="form-item">
              <label class="form-label">转让计税依据</label>
              <el-input
                v-model="formData.transferTaxBasis"
                placeholder="请输入计税依据"
                clearable
                class="form-input"
              />
            </div>
          </div>

          <div class="form-row">
            <div class="form-item">
              <label class="form-label">实际成交含税金额</label>
              <el-input
                v-model="formData.actualTransactionAmount"
                placeholder="请输入金额"
                clearable
                class="form-input"
              />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部区域 -->
    <div class="bottom-section">
      <!-- 开票主体信息 -->
      <div class="bottom-left">
        <div class="form-header">
          <span class="header-text">开票主体</span>
        </div>
        <div class="company-list">
          <div
            v-for="company in companyList"
            :key="company.value"
            class="company-item"
          >
            <span class="company-name">{{ company.label }}</span>
            <span class="company-type">{{ company.type }}</span>
          </div>
        </div>
      </div>

      <!-- 征收方式 -->
      <div class="bottom-middle">
        <div class="form-header">
          <span class="header-text">征收方式</span>
        </div>
        <div class="collection-methods">
          <div class="method-item">
            <span class="method-label">查账征收(查账)</span>
            <div class="method-options">
              <el-radio-group v-model="formData.collectionMethod1">
                <el-radio label="1">普通征收</el-radio>
                <el-radio label="2">重点征收</el-radio>
                <el-radio label="3">查账征收</el-radio>
                <el-radio label="4">查验征收</el-radio>
              </el-radio-group>
            </div>
          </div>

          <div class="method-item">
            <span class="method-label">查账征收</span>
            <div class="method-options">
              <el-radio-group v-model="formData.collectionMethod2">
                <el-radio label="1">重点征收</el-radio>
                <el-radio label="2">查账征收</el-radio>
                <el-radio label="3">查验征收</el-radio>
                <el-radio label="4">查账征收报告</el-radio>
              </el-radio-group>
            </div>
          </div>
        </div>
      </div>

      <!-- 红字发票申请 -->
      <div class="bottom-right">
        <div class="form-header">
          <span class="header-text">红字发票申请</span>
        </div>
        <div class="red-invoice-form">
          <div class="form-row">
            <div class="form-item">
              <label class="form-label">企业统一编码</label>
              <el-input
                v-model="formData.companyUnifiedCode"
                placeholder="请输入企业统一编码"
                clearable
                class="form-input"
              />
            </div>
          </div>

          <div class="form-row">
            <div class="form-item">
              <label class="form-label">登录账号</label>
              <el-input
                v-model="formData.loginAccount"
                placeholder="请输入登录账号"
                clearable
                class="form-input"
              />
            </div>
          </div>

          <div class="form-row">
            <div class="form-item">
              <label class="form-label">行政区划号(含县级)</label>
              <el-input
                v-model="formData.administrativeCode"
                placeholder="请输入行政区划号"
                clearable
                class="form-input"
              />
            </div>
          </div>

          <div class="form-row">
            <div class="form-item">
              <label class="form-label">检验设备代码</label>
              <el-input
                v-model="formData.inspectionDeviceCode"
                placeholder="请输入检验设备代码"
                clearable
                class="form-input"
              />
            </div>
          </div>

          <div class="form-row">
            <div class="form-item">
              <label class="form-label">特殊事项</label>
              <el-input
                v-model="formData.specialMatters"
                placeholder="请输入特殊事项"
                clearable
                class="form-input"
              />
            </div>
          </div>

          <div class="form-row">
            <div class="form-item">
              <label class="form-label">征收方式</label>
              <el-select
                v-model="formData.redInvoiceCollectionMethod"
                placeholder="请选择征收方式"
                clearable
                class="form-input"
              >
                <el-option label="查账征收" value="audit" />
                <el-option label="核定征收" value="approved" />
                <el-option label="代扣代缴" value="withholding" />
              </el-select>
            </div>
          </div>

          <div class="form-row">
            <div class="form-item">
              <label class="form-label">销售方名称</label>
              <el-input
                v-model="formData.sellerName"
                placeholder="请输入销售方名称"
                clearable
                class="form-input"
              />
            </div>
          </div>

          <div class="form-row">
            <div class="form-item">
              <label class="form-label">销售方纳税人识别号</label>
              <el-input
                v-model="formData.sellerTaxNumber"
                placeholder="请输入纳税人识别号"
                clearable
                class="form-input"
              />
            </div>
          </div>

          <div class="form-row">
            <div class="form-item">
              <label class="form-label">销售方地址</label>
              <el-input
                v-model="formData.sellerAddress"
                placeholder="请输入销售方地址"
                clearable
                class="form-input"
              />
            </div>
          </div>

          <div class="form-row">
            <div class="form-item">
              <label class="form-label">销售方电话</label>
              <el-input
                v-model="formData.sellerPhone"
                placeholder="请输入销售方电话"
                clearable
                class="form-input"
              />
            </div>
          </div>

          <div class="form-row">
            <div class="form-item">
              <label class="form-label">销售方开户行及账号</label>
              <el-input
                v-model="formData.sellerBankInfo"
                placeholder="请输入开户行及账号"
                clearable
                class="form-input"
              />
            </div>
          </div>

          <div class="form-row">
            <div class="form-item">
              <label class="form-label">购买方名称</label>
              <el-input
                v-model="formData.buyerName"
                placeholder="请输入购买方名称"
                clearable
                class="form-input"
              />
            </div>
          </div>

          <div class="form-row">
            <div class="form-item">
              <label class="form-label">购买方纳税人识别号</label>
              <el-input
                v-model="formData.buyerTaxNumber"
                placeholder="请输入纳税人识别号"
                clearable
                class="form-input"
              />
            </div>
          </div>

          <div class="form-row">
            <div class="form-item">
              <label class="form-label">购买方地址</label>
              <el-input
                v-model="formData.buyerAddress"
                placeholder="请输入购买方地址"
                clearable
                class="form-input"
              />
            </div>
          </div>

          <div class="form-row">
            <div class="form-item">
              <label class="form-label">购买方电话</label>
              <el-input
                v-model="formData.buyerPhone"
                placeholder="请输入购买方电话"
                clearable
                class="form-input"
              />
            </div>
          </div>

          <div class="form-row">
            <div class="form-item">
              <label class="form-label">购买方开户行及账号</label>
              <el-input
                v-model="formData.buyerBankInfo"
                placeholder="请输入开户行及账号"
                clearable
                class="form-input"
              />
            </div>
          </div>

          <div class="form-row">
            <div class="form-item">
              <label class="form-label">申请人</label>
              <el-input
                v-model="formData.applicant"
                placeholder="请输入申请人"
                clearable
                class="form-input"
              />
            </div>
          </div>

          <div class="form-row">
            <div class="form-item">
              <label class="form-label">是否为超限发票</label>
              <el-select
                v-model="formData.isOverlimitInvoice"
                placeholder="请选择"
                clearable
                class="form-input"
              >
                <el-option label="是" value="yes" />
                <el-option label="否" value="no" />
              </el-select>
            </div>
          </div>

          <div class="form-row">
            <div class="form-item">
              <label class="form-label">原发票号码</label>
              <el-input
                v-model="formData.originalInvoiceNumber"
                placeholder="请输入原发票号码"
                clearable
                class="form-input"
              />
            </div>
          </div>

          <div class="form-row">
            <div class="form-item">
              <label class="form-label">原开票日期</label>
              <el-date-picker
                v-model="formData.originalInvoiceDate"
                type="date"
                placeholder="选择原开票日期"
                value-format="yyyy-MM-dd"
                class="form-input"
              />
            </div>
          </div>

          <div class="form-row">
            <div class="form-item">
              <label class="form-label">冲红原因代码</label>
              <el-input
                v-model="formData.redReasonCode"
                placeholder="请输入冲红原因代码"
                clearable
                class="form-input"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {
  getInvBodyList,
  invApplyRecordKind,
  invApplyRecordOpenType
} from '../../api'

export default {
  name: 'InvoiceInfo',
  data() {
    return {
      // 统计数据
      statisticsData: [
        { key: 'special_vat', label: '增值税专用发票', percentage: 13, highlight: true },
        { key: 'ordinary', label: '普通发票', percentage: 3, highlight: false },
        { key: 'special_real_estate', label: '不动产专用发票', percentage: 6, highlight: false },
        { key: 'real_estate_ordinary', label: '不动产普通发票', percentage: 9, highlight: false },
        { key: 'real_estate_sales', label: '不动产销售', percentage: 9, highlight: false }
      ],

      // 表单数据
      formData: {
        // 普通票字段
        invoiceBody: '',
        unit: '',
        taxNumber: '',
        address: '',
        phone: '',
        bankName: '',
        bankAccount: '',

        // 不动产租赁字段
        realEstateAddress: '',
        detailAddress: '',
        startPeriod: '',
        endPeriod: '',
        propertyCertificate: '',
        houseNature: '',

        // 不动产销售字段
        saleRealEstateAddress: '',
        saleDetailAddress: '',
        salePropertyCertificate: '',
        saleHouseNature: '',
        contractRecordNumber: '',
        landValueTaxNumber: '',
        transferTaxBasis: '',
        actualTransactionAmount: '',

        // 征收方式
        collectionMethod1: '',
        collectionMethod2: '',

        // 红字发票申请字段
        companyUnifiedCode: '',
        loginAccount: '',
        administrativeCode: '',
        inspectionDeviceCode: '',
        specialMatters: '',
        redInvoiceCollectionMethod: '',
        sellerName: '',
        sellerTaxNumber: '',
        sellerAddress: '',
        sellerPhone: '',
        sellerBankInfo: '',
        buyerName: '',
        buyerTaxNumber: '',
        buyerAddress: '',
        buyerPhone: '',
        buyerBankInfo: '',
        applicant: '',
        isOverlimitInvoice: '',
        originalInvoiceNumber: '',
        originalInvoiceDate: '',
        redReasonCode: ''
      },

      // 下拉选项
      invoiceBodyOptions: [],

      // 开票主体公司列表
      companyList: [
        { label: '合肥中宇创业科技有限公司', value: 'hefei_zhongyu', type: '可开系统发票' },
        { label: '合肥中宇创业科技有限公司', value: 'hefei_zhongyu_2', type: '可开系统发票' },
        { label: '安徽中宇创业科技有限公司', value: 'anhui_zhongyu', type: '可开系统发票' },
        { label: '合肥中宇未来投资管理有限公司', value: 'hefei_weilai', type: '可开系统发票' }
      ]
    }
  },

  mounted() {
    this.getInvoiceBodyOptions()
  },

  methods: {
    // 获取开票主体选项
    async getInvoiceBodyOptions() {
      try {
        const res = await getInvBodyList()
        this.invoiceBodyOptions = res.map(item => ({
          label: item.label,
          value: item.key
        }))
      } catch (error) {
        console.error('获取开票主体失败:', error)
      }
    }
  }
  }
}
</script>

<style scoped lang="scss">
.invoice-info-container {
  padding: 16px;
  background: #fff;
  border-radius: 4px;

  .main-content {
    display: flex;
    gap: 16px;
    margin-bottom: 24px;
  }

  // 左侧统计区域
  .left-statistics {
    flex: 0 0 280px;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 16px;

    .statistics-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      padding-bottom: 8px;
      border-bottom: 1px solid #dee2e6;

      .header-text {
        font-weight: 600;
        color: #333;
        font-size: 14px;
      }

      .statistics-label {
        font-size: 12px;
        color: #6c757d;
        background: #e9ecef;
        padding: 2px 8px;
        border-radius: 12px;
      }
    }

    .statistics-list {
      .statistics-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 12px;
        margin-bottom: 4px;
        border-radius: 4px;
        transition: background-color 0.2s;

        &:hover {
          background: #e9ecef;
        }

        &.highlight {
          background: #fff3cd;
          border: 1px solid #ffeaa7;

          .item-percentage {
            color: #856404;
            font-weight: 600;
          }
        }

        .item-label {
          font-size: 13px;
          color: #495057;
        }

        .item-percentage {
          font-size: 13px;
          color: #6c757d;
          font-weight: 500;
        }
      }
    }
  }

  // 中间和右侧表单区域
  .middle-form,
  .right-form {
    flex: 1;
    background: #fff;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 16px;

    .form-header {
      margin-bottom: 16px;
      padding-bottom: 8px;
      border-bottom: 1px solid #dee2e6;

      .header-text {
        font-weight: 600;
        color: #333;
        font-size: 14px;
      }
    }

    .form-content {
      .form-row {
        margin-bottom: 16px;

        .form-item {
          .form-label {
            display: block;
            margin-bottom: 4px;
            font-size: 13px;
            color: #495057;
            font-weight: 500;

            &::before {
              content: '*';
              color: #dc3545;
              margin-right: 2px;
              display: none;
            }

            &:has-text('*') {
              &::before {
                display: inline;
              }
            }
          }

          .form-input {
            width: 100%;

            :deep(.el-input__inner) {
              height: 32px;
              line-height: 32px;
              font-size: 13px;
            }

            :deep(.el-select) {
              width: 100%;
            }

            :deep(.el-date-editor) {
              width: 100%;
            }
          }
        }
      }
    }
  }

  // 底部区域
  .bottom-section {
    display: flex;
    gap: 16px;

    .bottom-left,
    .bottom-middle,
    .bottom-right {
      background: #fff;
      border: 1px solid #e9ecef;
      border-radius: 4px;
      padding: 16px;
    }

    .bottom-left {
      flex: 0 0 280px;

      .company-list {
        .company-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 8px 12px;
          margin-bottom: 8px;
          background: #f8f9fa;
          border-radius: 4px;
          border: 1px solid #e9ecef;

          .company-name {
            font-size: 13px;
            color: #495057;
            flex: 1;
          }

          .company-type {
            font-size: 12px;
            color: #28a745;
            background: #d4edda;
            padding: 2px 8px;
            border-radius: 12px;
            white-space: nowrap;
          }
        }
      }
    }

    .bottom-middle {
      flex: 0 0 320px;

      .collection-methods {
        .method-item {
          margin-bottom: 16px;

          .method-label {
            display: block;
            margin-bottom: 8px;
            font-size: 13px;
            color: #495057;
            font-weight: 500;
          }

          .method-options {
            :deep(.el-radio-group) {
              display: flex;
              flex-direction: column;
              gap: 4px;

              .el-radio {
                margin-right: 0;
                margin-bottom: 4px;

                .el-radio__label {
                  font-size: 12px;
                }
              }
            }
          }
        }
      }
    }

    .bottom-right {
      flex: 1;

      .red-invoice-form {
        .form-row {
          margin-bottom: 12px;

          .form-item {
            .form-label {
              display: block;
              margin-bottom: 4px;
              font-size: 12px;
              color: #495057;
              font-weight: 500;
            }

            .form-input {
              width: 100%;

              :deep(.el-input__inner) {
                height: 28px;
                line-height: 28px;
                font-size: 12px;
              }

              :deep(.el-select) {
                width: 100%;
              }

              :deep(.el-date-editor) {
                width: 100%;
              }
            }
          }
        }
      }
    }
  }

  // 通用样式
  .form-header {
    .header-text {
      position: relative;

      &::before {
        content: '';
        position: absolute;
        left: -8px;
        top: 50%;
        transform: translateY(-50%);
        width: 3px;
        height: 16px;
        background: #007bff;
        border-radius: 2px;
      }
    }
  }

  // 响应式处理
  @media (max-width: 1200px) {
    .main-content {
      flex-direction: column;
    }

    .bottom-section {
      flex-direction: column;
    }
  }
}

// 工具类
.m-t-16 {
  margin-top: 16px;
}
</style>
