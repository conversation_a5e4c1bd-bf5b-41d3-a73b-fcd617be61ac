<template>
  <div class="invoice-info-container">
    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 左侧统计区域 -->
      <div class="left-statistics">
        <div class="statistics-header">
          <span class="header-text">自主开票的黄目</span>
          <span class="statistics-label">统计数据</span>
        </div>
        <div class="statistics-list">
          <div
            v-for="item in statisticsData"
            :key="item.key"
            class="statistics-item"
            :class="{ 'highlight': item.highlight }"
          >
            <span class="item-label">{{ item.label }}</span>
            <span class="item-percentage">{{ item.percentage }}%</span>
          </div>
        </div>
      </div>

      <!-- 中间普通票发票字段 -->
      <div class="middle-form">
        <div class="form-header">
          <span class="header-text">普通票发票字段</span>
        </div>
        <el-form
          :model="formData"
          :rules="formRules"
          ref="normalInvoiceForm"
          label-width="120px"
          size="small"
        >
          <el-form-item
            v-for="field in normalInvoiceFields"
            :key="field.prop"
            :label="field.label"
            :prop="field.prop"
            :required="field.required"
          >
            <!-- 下拉选择框 -->
            <el-select
              v-if="field.type === 'select'"
              v-model="formData[field.prop]"
              :placeholder="field.placeholder"
              clearable
              style="width: 100%"
            >
              <el-option
                v-for="option in field.options"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>

            <!-- 普通输入框 -->
            <el-input
              v-else
              v-model="formData[field.prop]"
              :placeholder="field.placeholder"
              clearable
            />
          </el-form-item>
        </el-form>
      </div>

      <!-- 右侧特殊票种字段 -->
      <div class="right-form">
        <!-- 不动产租赁 -->
        <div class="form-section">
          <div class="form-header">
            <span class="header-text">特殊票种发票字段-不动产租赁</span>
          </div>
          <el-form
            :model="formData"
            :rules="formRules"
            ref="realEstateRentForm"
            label-width="140px"
            size="small"
          >
            <el-form-item
              v-for="field in realEstateRentFields"
              :key="field.prop"
              :label="field.label"
              :prop="field.prop"
              :required="field.required"
            >
              <!-- 日期选择器 -->
              <el-date-picker
                v-if="field.type === 'date'"
                v-model="formData[field.prop]"
                type="date"
                :placeholder="field.placeholder"
                value-format="yyyy-MM-dd"
                style="width: 100%"
              />

              <!-- 下拉选择框 -->
              <el-select
                v-else-if="field.type === 'select'"
                v-model="formData[field.prop]"
                :placeholder="field.placeholder"
                clearable
                style="width: 100%"
              >
                <el-option
                  v-for="option in field.options"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>

              <!-- 普通输入框 -->
              <el-input
                v-else
                v-model="formData[field.prop]"
                :placeholder="field.placeholder"
                clearable
              />
            </el-form-item>
          </el-form>
        </div>

        <!-- 不动产销售 -->
        <div class="form-section">
          <div class="form-header">
            <span class="header-text">特殊票种发票字段-不动产销售</span>
          </div>
          <el-form
            :model="formData"
            :rules="formRules"
            ref="realEstateSaleForm"
            label-width="140px"
            size="small"
          >
            <el-form-item
              v-for="field in realEstateSaleFields"
              :key="field.prop"
              :label="field.label"
              :prop="field.prop"
              :required="field.required"
            >
              <!-- 下拉选择框 -->
              <el-select
                v-if="field.type === 'select'"
                v-model="formData[field.prop]"
                :placeholder="field.placeholder"
                clearable
                style="width: 100%"
              >
                <el-option
                  v-for="option in field.options"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>

              <!-- 普通输入框 -->
              <el-input
                v-else
                v-model="formData[field.prop]"
                :placeholder="field.placeholder"
                clearable
              />
            </el-form-item>
          </el-form>
        </div>
      </div>
    </div>

    <!-- 底部区域 -->
    <div class="bottom-section">
      <!-- 开票主体信息 -->
      <div class="bottom-left">
        <div class="form-header">
          <span class="header-text">开票主体</span>
        </div>
        <div class="company-list">
          <div
            v-for="company in companyList"
            :key="company.value"
            class="company-item"
          >
            <span class="company-name">{{ company.label }}</span>
            <span class="company-type">{{ company.type }}</span>
          </div>
        </div>
      </div>

      <!-- 征收方式 -->
      <div class="bottom-middle">
        <div class="form-header">
          <span class="header-text">征收方式</span>
        </div>
        <div class="collection-methods">
          <div
            v-for="method in collectionMethods"
            :key="method.key"
            class="method-item"
          >
            <span class="method-label">{{ method.label }}</span>
            <div class="method-options">
              <el-radio-group v-model="formData[method.prop]">
                <el-radio
                  v-for="option in method.options"
                  :key="option.value"
                  :label="option.value"
                >
                  {{ option.label }}
                </el-radio>
              </el-radio-group>
            </div>
          </div>
        </div>
      </div>

      <!-- 红字发票申请 -->
      <div class="bottom-right">
        <div class="form-header">
          <span class="header-text">红字发票申请</span>
        </div>
        <el-form
          :model="formData"
          :rules="formRules"
          ref="redInvoiceForm"
          label-width="140px"
          size="small"
        >
          <el-form-item
            v-for="field in redInvoiceFields"
            :key="field.prop"
            :label="field.label"
            :prop="field.prop"
            :required="field.required"
          >
            <!-- 日期选择器 -->
            <el-date-picker
              v-if="field.type === 'date'"
              v-model="formData[field.prop]"
              type="date"
              :placeholder="field.placeholder"
              value-format="yyyy-MM-dd"
              style="width: 100%"
            />

            <!-- 下拉选择框 -->
            <el-select
              v-else-if="field.type === 'select'"
              v-model="formData[field.prop]"
              :placeholder="field.placeholder"
              clearable
              style="width: 100%"
            >
              <el-option
                v-for="option in field.options"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>

            <!-- 普通输入框 -->
            <el-input
              v-else
              v-model="formData[field.prop]"
              :placeholder="field.placeholder"
              clearable
            />
          </el-form-item>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script>
import {
  getInvBodyList,
  invApplyRecordKind,
  invApplyRecordOpenType
} from '../../api'

export default {
  name: 'InvoiceInfo',
  data() {
    return {
      // 统计数据
      statisticsData: [
        { key: 'special_vat', label: '增值税专用发票', percentage: 13, highlight: true },
        { key: 'ordinary', label: '普通发票', percentage: 3, highlight: false },
        { key: 'special_real_estate', label: '不动产专用发票', percentage: 6, highlight: false },
        { key: 'real_estate_ordinary', label: '不动产普通发票', percentage: 9, highlight: false },
        { key: 'real_estate_sales', label: '不动产销售', percentage: 9, highlight: false }
      ],

      // 表单数据
      formData: {
        // 普通票字段
        invoiceBody: '',
        unit: '',
        taxNumber: '',
        address: '',
        phone: '',
        bankName: '',
        bankAccount: '',

        // 不动产租赁字段
        realEstateAddress: '',
        detailAddress: '',
        startPeriod: '',
        endPeriod: '',
        propertyCertificate: '',
        houseNature: '',

        // 不动产销售字段
        saleRealEstateAddress: '',
        saleDetailAddress: '',
        salePropertyCertificate: '',
        saleHouseNature: '',
        contractRecordNumber: '',
        landValueTaxNumber: '',
        transferTaxBasis: '',
        actualTransactionAmount: '',

        // 征收方式
        collectionMethod1: '',
        collectionMethod2: '',

        // 红字发票申请字段
        companyUnifiedCode: '',
        loginAccount: '',
        administrativeCode: '',
        inspectionDeviceCode: '',
        specialMatters: '',
        redInvoiceCollectionMethod: '',
        sellerName: '',
        sellerTaxNumber: '',
        sellerAddress: '',
        sellerPhone: '',
        sellerBankInfo: '',
        buyerName: '',
        buyerTaxNumber: '',
        buyerAddress: '',
        buyerPhone: '',
        buyerBankInfo: '',
        applicant: '',
        isOverlimitInvoice: '',
        originalInvoiceNumber: '',
        originalInvoiceDate: '',
        redReasonCode: ''
      },

      // 表单验证规则
      formRules: {
        invoiceBody: [{ required: true, message: '请选择开票主体', trigger: 'change' }],
        realEstateAddress: [{ required: true, message: '请输入不动产地址', trigger: 'blur' }],
        saleRealEstateAddress: [{ required: true, message: '请输入不动产地址', trigger: 'blur' }]
      },

      // 下拉选项
      invoiceBodyOptions: [],

      // 开票主体公司列表
      companyList: [
        { label: '合肥中宇创业科技有限公司', value: 'hefei_zhongyu', type: '可开系统发票' },
        { label: '合肥中宇创业科技有限公司', value: 'hefei_zhongyu_2', type: '可开系统发票' },
        { label: '安徽中宇创业科技有限公司', value: 'anhui_zhongyu', type: '可开系统发票' },
        { label: '合肥中宇未来投资管理有限公司', value: 'hefei_weilai', type: '可开系统发票' }
      ]
    }
  },

  computed: {
    // 普通票发票字段配置
    normalInvoiceFields() {
      return [
        {
          prop: 'invoiceBody',
          label: '开票主体',
          type: 'select',
          placeholder: '请选择开票主体',
          required: true,
          options: this.invoiceBodyOptions
        },
        {
          prop: 'unit',
          label: '单位',
          type: 'input',
          placeholder: '请输入单位',
          required: false
        },
        {
          prop: 'taxNumber',
          label: '税号',
          type: 'input',
          placeholder: '请输入税号',
          required: false
        },
        {
          prop: 'address',
          label: '地址',
          type: 'input',
          placeholder: '请输入地址',
          required: false
        },
        {
          prop: 'phone',
          label: '电话',
          type: 'input',
          placeholder: '请输入电话',
          required: false
        },
        {
          prop: 'bankName',
          label: '开户行',
          type: 'input',
          placeholder: '请输入开户行',
          required: false
        },
        {
          prop: 'bankAccount',
          label: '账号',
          type: 'input',
          placeholder: '请输入账号',
          required: false
        }
      ]
    },

    // 不动产租赁字段配置
    realEstateRentFields() {
      return [
        {
          prop: 'realEstateAddress',
          label: '*不动产地址',
          type: 'input',
          placeholder: '请输入不动产地址',
          required: true
        },
        {
          prop: 'detailAddress',
          label: '详细地址',
          type: 'input',
          placeholder: '请输入详细地址',
          required: false
        },
        {
          prop: 'startPeriod',
          label: '起始期间',
          type: 'date',
          placeholder: '选择起始期间',
          required: false
        },
        {
          prop: 'endPeriod',
          label: '终止期间',
          type: 'date',
          placeholder: '选择终止期间',
          required: false
        },
        {
          prop: 'propertyCertificate',
          label: '产权证书/不动产权证号',
          type: 'input',
          placeholder: '请输入证书号',
          required: false
        },
        {
          prop: 'houseNature',
          label: '房屋性质',
          type: 'select',
          placeholder: '请选择房屋性质',
          required: false,
          options: [
            { label: '住宅', value: 'residential' },
            { label: '商业', value: 'commercial' },
            { label: '办公', value: 'office' }
          ]
        }
      ]
    },

    // 不动产销售字段配置
    realEstateSaleFields() {
      return [
        {
          prop: 'saleRealEstateAddress',
          label: '*不动产地址',
          type: 'input',
          placeholder: '请输入不动产地址',
          required: true
        },
        {
          prop: 'saleDetailAddress',
          label: '详细地址',
          type: 'input',
          placeholder: '请输入详细地址',
          required: false
        },
        {
          prop: 'salePropertyCertificate',
          label: '产权证书/不动产权证号',
          type: 'input',
          placeholder: '请输入证书号',
          required: false
        },
        {
          prop: 'saleHouseNature',
          label: '房屋性质',
          type: 'select',
          placeholder: '请选择房屋性质',
          required: false,
          options: [
            { label: '住宅', value: 'residential' },
            { label: '商业', value: 'commercial' },
            { label: '办公', value: 'office' }
          ]
        },
        {
          prop: 'contractRecordNumber',
          label: '黄金合同备案号',
          type: 'input',
          placeholder: '请输入合同备案号',
          required: false
        },
        {
          prop: 'landValueTaxNumber',
          label: '土地增值税项目编号',
          type: 'input',
          placeholder: '请输入项目编号',
          required: false
        },
        {
          prop: 'transferTaxBasis',
          label: '转让计税依据',
          type: 'input',
          placeholder: '请输入计税依据',
          required: false
        },
        {
          prop: 'actualTransactionAmount',
          label: '实际成交含税金额',
          type: 'input',
          placeholder: '请输入金额',
          required: false
        }
      ]
    },

    // 征收方式配置
    collectionMethods() {
      return [
        {
          key: 'method1',
          label: '查账征收(查账)',
          prop: 'collectionMethod1',
          options: [
            { label: '普通征收', value: '1' },
            { label: '重点征收', value: '2' },
            { label: '查账征收', value: '3' },
            { label: '查验征收', value: '4' }
          ]
        },
        {
          key: 'method2',
          label: '查账征收',
          prop: 'collectionMethod2',
          options: [
            { label: '重点征收', value: '1' },
            { label: '查账征收', value: '2' },
            { label: '查验征收', value: '3' },
            { label: '查账征收报告', value: '4' }
          ]
        }
      ]
    },

    // 红字发票申请字段配置
    redInvoiceFields() {
      return [
        {
          prop: 'companyUnifiedCode',
          label: '企业统一编码',
          type: 'input',
          placeholder: '请输入企业统一编码',
          required: false
        },
        {
          prop: 'loginAccount',
          label: '登录账号',
          type: 'input',
          placeholder: '请输入登录账号',
          required: false
        },
        {
          prop: 'administrativeCode',
          label: '行政区划号(含县级)',
          type: 'input',
          placeholder: '请输入行政区划号',
          required: false
        },
        {
          prop: 'inspectionDeviceCode',
          label: '检验设备代码',
          type: 'input',
          placeholder: '请输入检验设备代码',
          required: false
        },
        {
          prop: 'specialMatters',
          label: '特殊事项',
          type: 'input',
          placeholder: '请输入特殊事项',
          required: false
        },
        {
          prop: 'redInvoiceCollectionMethod',
          label: '征收方式',
          type: 'select',
          placeholder: '请选择征收方式',
          required: false,
          options: [
            { label: '查账征收', value: 'audit' },
            { label: '核定征收', value: 'approved' },
            { label: '代扣代缴', value: 'withholding' }
          ]
        },
        {
          prop: 'sellerName',
          label: '销售方名称',
          type: 'input',
          placeholder: '请输入销售方名称',
          required: false
        },
        {
          prop: 'sellerTaxNumber',
          label: '销售方纳税人识别号',
          type: 'input',
          placeholder: '请输入纳税人识别号',
          required: false
        },
        {
          prop: 'sellerAddress',
          label: '销售方地址',
          type: 'input',
          placeholder: '请输入销售方地址',
          required: false
        },
        {
          prop: 'sellerPhone',
          label: '销售方电话',
          type: 'input',
          placeholder: '请输入销售方电话',
          required: false
        },
        {
          prop: 'sellerBankInfo',
          label: '销售方开户行及账号',
          type: 'input',
          placeholder: '请输入开户行及账号',
          required: false
        },
        {
          prop: 'buyerName',
          label: '购买方名称',
          type: 'input',
          placeholder: '请输入购买方名称',
          required: false
        },
        {
          prop: 'buyerTaxNumber',
          label: '购买方纳税人识别号',
          type: 'input',
          placeholder: '请输入纳税人识别号',
          required: false
        },
        {
          prop: 'buyerAddress',
          label: '购买方地址',
          type: 'input',
          placeholder: '请输入购买方地址',
          required: false
        },
        {
          prop: 'buyerPhone',
          label: '购买方电话',
          type: 'input',
          placeholder: '请输入购买方电话',
          required: false
        },
        {
          prop: 'buyerBankInfo',
          label: '购买方开户行及账号',
          type: 'input',
          placeholder: '请输入开户行及账号',
          required: false
        },
        {
          prop: 'applicant',
          label: '申请人',
          type: 'input',
          placeholder: '请输入申请人',
          required: false
        },
        {
          prop: 'isOverlimitInvoice',
          label: '是否为超限发票',
          type: 'select',
          placeholder: '请选择',
          required: false,
          options: [
            { label: '是', value: 'yes' },
            { label: '否', value: 'no' }
          ]
        },
        {
          prop: 'originalInvoiceNumber',
          label: '原发票号码',
          type: 'input',
          placeholder: '请输入原发票号码',
          required: false
        },
        {
          prop: 'originalInvoiceDate',
          label: '原开票日期',
          type: 'date',
          placeholder: '选择原开票日期',
          required: false
        },
        {
          prop: 'redReasonCode',
          label: '冲红原因代码',
          type: 'input',
          placeholder: '请输入冲红原因代码',
          required: false
        }
      ]
    }
  },

  mounted() {
    this.getInvoiceBodyOptions()
  },

  methods: {
    // 获取开票主体选项
    async getInvoiceBodyOptions() {
      try {
        const res = await getInvBodyList()
        this.invoiceBodyOptions = res.map(item => ({
          label: item.label,
          value: item.key
        }))
      } catch (error) {
        console.error('获取开票主体失败:', error)
      }
    },

    // 表单验证
    validateForm() {
      let isValid = true
      const forms = ['normalInvoiceForm', 'realEstateRentForm', 'realEstateSaleForm', 'redInvoiceForm']

      forms.forEach(formRef => {
        if (this.$refs[formRef]) {
          this.$refs[formRef].validate((valid) => {
            if (!valid) {
              isValid = false
            }
          })
        }
      })

      return isValid
    },

    // 重置表单
    resetForm() {
      const forms = ['normalInvoiceForm', 'realEstateRentForm', 'realEstateSaleForm', 'redInvoiceForm']

      forms.forEach(formRef => {
        if (this.$refs[formRef]) {
          this.$refs[formRef].resetFields()
        }
      })
    },

    // 获取表单数据
    getFormData() {
      if (this.validateForm()) {
        return this.formData
      }
      return null
    }
  }
  }
}
</script>

<style scoped lang="scss">
.invoice-info-container {
  padding: 16px;
  background: #fff;
  border-radius: 4px;

  .main-content {
    display: flex;
    gap: 16px;
    margin-bottom: 24px;
  }

  // 左侧统计区域
  .left-statistics {
    flex: 0 0 280px;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 16px;

    .statistics-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      padding-bottom: 8px;
      border-bottom: 1px solid #dee2e6;

      .header-text {
        font-weight: 600;
        color: #333;
        font-size: 14px;
      }

      .statistics-label {
        font-size: 12px;
        color: #6c757d;
        background: #e9ecef;
        padding: 2px 8px;
        border-radius: 12px;
      }
    }

    .statistics-list {
      .statistics-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 12px;
        margin-bottom: 4px;
        border-radius: 4px;
        transition: background-color 0.2s;

        &:hover {
          background: #e9ecef;
        }

        &.highlight {
          background: #fff3cd;
          border: 1px solid #ffeaa7;

          .item-percentage {
            color: #856404;
            font-weight: 600;
          }
        }

        .item-label {
          font-size: 13px;
          color: #495057;
        }

        .item-percentage {
          font-size: 13px;
          color: #6c757d;
          font-weight: 500;
        }
      }
    }
  }

  // 中间和右侧表单区域
  .middle-form,
  .right-form {
    flex: 1;
    background: #fff;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 16px;

    .form-section {
      margin-bottom: 24px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .form-header {
      margin-bottom: 16px;
      padding-bottom: 8px;
      border-bottom: 1px solid #dee2e6;
      position: relative;

      .header-text {
        font-weight: 600;
        color: #333;
        font-size: 14px;
        padding-left: 12px;

        &::before {
          content: '';
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
          width: 3px;
          height: 16px;
          background: #007bff;
          border-radius: 2px;
        }
      }
    }

    // Element UI 表单样式调整
    :deep(.el-form) {
      .el-form-item {
        margin-bottom: 16px;

        .el-form-item__label {
          font-size: 13px;
          color: #495057;
          font-weight: 500;
          line-height: 1.4;
          padding-bottom: 4px;
        }

        .el-form-item__content {
          .el-input,
          .el-select,
          .el-date-editor {
            width: 100%;

            .el-input__inner {
              height: 32px;
              line-height: 32px;
              font-size: 13px;
              border: 1px solid #dcdfe6;
              border-radius: 4px;

              &:focus {
                border-color: #409eff;
              }
            }
          }

          .el-select {
            .el-input__inner {
              cursor: pointer;
            }
          }

          .el-date-editor {
            .el-input__inner {
              padding-left: 30px;
            }
          }
        }

        &.is-required {
          .el-form-item__label::before {
            content: '*';
            color: #f56c6c;
            margin-right: 4px;
          }
        }
      }
    }
  }

  // 底部区域
  .bottom-section {
    display: flex;
    gap: 16px;

    .bottom-left,
    .bottom-middle,
    .bottom-right {
      background: #fff;
      border: 1px solid #e9ecef;
      border-radius: 4px;
      padding: 16px;
    }

    .bottom-left {
      flex: 0 0 280px;

      .company-list {
        .company-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 8px 12px;
          margin-bottom: 8px;
          background: #f8f9fa;
          border-radius: 4px;
          border: 1px solid #e9ecef;
          transition: background-color 0.2s;

          &:hover {
            background: #e9ecef;
          }

          .company-name {
            font-size: 13px;
            color: #495057;
            flex: 1;
            margin-right: 8px;
          }

          .company-type {
            font-size: 12px;
            color: #28a745;
            background: #d4edda;
            padding: 2px 8px;
            border-radius: 12px;
            white-space: nowrap;
          }
        }
      }
    }

    .bottom-middle {
      flex: 0 0 320px;

      .collection-methods {
        .method-item {
          margin-bottom: 20px;

          &:last-child {
            margin-bottom: 0;
          }

          .method-label {
            display: block;
            margin-bottom: 8px;
            font-size: 13px;
            color: #495057;
            font-weight: 500;
          }

          .method-options {
            :deep(.el-radio-group) {
              display: flex;
              flex-direction: column;
              gap: 6px;

              .el-radio {
                margin-right: 0;
                margin-bottom: 0;

                .el-radio__label {
                  font-size: 12px;
                  color: #606266;
                }

                .el-radio__input {
                  .el-radio__inner {
                    width: 14px;
                    height: 14px;
                  }
                }
              }
            }
          }
        }
      }
    }

    .bottom-right {
      flex: 1;

      // 红字发票申请表单样式
      :deep(.el-form) {
        .el-form-item {
          margin-bottom: 12px;

          .el-form-item__label {
            font-size: 12px;
            line-height: 1.3;
            padding-bottom: 2px;
          }

          .el-form-item__content {
            .el-input,
            .el-select,
            .el-date-editor {
              .el-input__inner {
                height: 28px;
                line-height: 28px;
                font-size: 12px;
              }
            }
          }
        }
      }
    }
  }

  // 响应式处理
  @media (max-width: 1200px) {
    .main-content {
      flex-direction: column;
    }

    .bottom-section {
      flex-direction: column;
    }

    .left-statistics,
    .bottom-left,
    .bottom-middle {
      flex: none;
    }
  }

  @media (max-width: 768px) {
    padding: 12px;

    .main-content,
    .bottom-section {
      gap: 12px;
    }

    .middle-form,
    .right-form,
    .bottom-left,
    .bottom-middle,
    .bottom-right {
      padding: 12px;
    }
  }
}
</style>
